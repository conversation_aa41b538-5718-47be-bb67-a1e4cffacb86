/**
 * Global Authentication Navigation Manager
 * Handles all navigation with authentication checks
 */

class AuthNavigation {
    constructor() {
        this.STORAGE_KEY = 'auth_return_url';
        console.log('🔐 AuthNavigation initialized');
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token || token === 'null' || token === '') {
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                // Token expired
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user_info');
                return false;
            }

            return true;
        } catch (error) {
            // Invalid token
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_info');
            return false;
        }
    }

    /**
     * Navigate to a URL with authentication check
     * @param {string} url - Target URL
     * @param {boolean} requiresAuth - Whether the target requires authentication
     */
    navigateTo(url, requiresAuth = false) {
        console.log(`🔐 Navigating to: ${url}, requires auth: ${requiresAuth}`);
        
        if (requiresAuth && !this.isAuthenticated()) {
            console.log('🔐 Authentication required, redirecting to login');
            this.saveReturnUrl(url);
            window.location.href = '/login/';
        } else {
            console.log('🔐 Direct navigation');
            window.location.href = url;
        }
    }

    /**
     * Go to login page and return to current page after login
     */
    goToLogin() {
        console.log('🔐 Going to login, will return to current page');
        this.saveReturnUrl(window.location.href);
        window.location.href = '/login/';
    }

    /**
     * Save the URL to return to after login
     */
    saveReturnUrl(url) {
        sessionStorage.setItem(this.STORAGE_KEY, url);
        console.log('🔐 Saved return URL:', url);
    }

    /**
     * Handle post-login redirect
     */
    handlePostLogin() {
        const returnUrl = sessionStorage.getItem(this.STORAGE_KEY);
        console.log('🔐 Post-login redirect - Return URL:', returnUrl);
        console.log('🔐 Current URL:', window.location.href);

        sessionStorage.removeItem(this.STORAGE_KEY);

        if (returnUrl && returnUrl !== window.location.href && returnUrl !== '/login/') {
            console.log('🔐 Redirecting to saved URL:', returnUrl);
            window.location.replace(returnUrl);
        } else {
            console.log('🔐 No valid return URL, going to home');
            window.location.replace('/');
        }
    }

    /**
     * Create a navigation link with authentication check
     * @param {string} url - Target URL
     * @param {string} text - Link text
     * @param {boolean} requiresAuth - Whether target requires authentication
     * @param {string} className - CSS classes
     */
    createLink(url, text, requiresAuth = false, className = '') {
        return `<a href="${url}" class="${className}" onclick="authNav.navigateTo('${url}', ${requiresAuth}); return false;">${text}</a>`;
    }

    /**
     * Create a login link that returns to current page
     * @param {string} text - Link text
     * @param {string} className - CSS classes
     */
    createLoginLink(text = 'Login', className = '') {
        return `<a href="/login/" class="${className}" onclick="authNav.goToLogin(); return false;">${text}</a>`;
    }

    /**
     * Setup all navigation links on the page
     */
    setupPageNavigation() {
        console.log('🔐 Setting up page navigation...');

        // Setup auth-required links
        const authRequiredLinks = document.querySelectorAll('[data-auth-required="true"]');
        console.log(`🔐 Found ${authRequiredLinks.length} auth-required links`);
        authRequiredLinks.forEach(element => {
            const originalHref = element.href;
            element.onclick = (e) => {
                e.preventDefault();
                this.navigateTo(originalHref, true);
                return false;
            };
        });

        // Setup login links
        const loginLinks = document.querySelectorAll('[data-login-link="true"]');
        console.log(`🔐 Found ${loginLinks.length} login links`);
        loginLinks.forEach(element => {
            element.onclick = (e) => {
                e.preventDefault();
                this.goToLogin();
                return false;
            };
        });

        // Setup regular navigation links
        const navLinks = document.querySelectorAll('[data-nav-link="true"]');
        console.log(`🔐 Found ${navLinks.length} navigation links`);
        navLinks.forEach(element => {
            const originalHref = element.href;
            element.onclick = (e) => {
                e.preventDefault();
                this.navigateTo(originalHref, false);
                return false;
            };
        });

        console.log('🔐 Page navigation setup complete');
    }
}

// Create global instance
const authNav = new AuthNavigation();

// Global functions for HTML onclick handlers
window.authNav = authNav;
window.navigateTo = (url, requiresAuth = false) => authNav.navigateTo(url, requiresAuth);
window.goToLogin = () => authNav.goToLogin();

// Auto-setup on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Setting up page navigation');
    authNav.setupPageNavigation();
});

console.log('🔐 AuthNavigation loaded');
