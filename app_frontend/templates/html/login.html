{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Login - HiSage Health</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="{% static 'js/auth_navigation.js' %}?v=1.0"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 450px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .login-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .forgot-password {
            text-align: center;
            margin-bottom: 20px;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }

        .register-link {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .register-link a:hover {
            text-decoration: underline;
        }

        /* Two-step login styles */
        .email-display {
            background: #f8f9fa;
            padding: 12px 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-weight: 500;
            color: #495057;
            margin-bottom: 10px;
        }

        .btn-link {
            background: none;
            border: none;
            color: #667eea;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
            padding: 5px 0;
        }

        .btn-link:hover {
            text-decoration: underline;
            color: #5a6fd8;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 14px;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .activation-buttons, .register-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        /* Back button removed */

        .required {
            color: #e74c3c;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .login-header {
                padding: 30px 20px;
            }
            
            .login-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-brain"></i> HiSage</h1>
            <p>AI-Powered Dementia Screening Platform</p>
        </div>

        <div class="login-form">
            <div id="loginMessage"></div>



            <!-- Step 1: Enter Email -->
            <form id="emailForm" style="display: block;">
                <div class="form-group">
                    <label for="email">Email Address <span class="required">*</span></label>
                    <input type="email" id="email" name="email" required placeholder="Enter your email address">
                </div>

                <button type="submit" id="emailBtn" class="login-btn">
                    <i class="fas fa-arrow-right"></i> Next
                </button>
            </form>

            <!-- Step 2: Enter Password -->
            <form id="passwordForm" style="display: none;">
                <div class="form-group">
                    <label for="emailDisplay">Email Address</label>
                    <div class="email-display" id="emailDisplay"></div>
                    <button type="button" class="btn-link" id="changeEmailBtn">
                        <i class="fas fa-edit"></i> Change Email
                    </button>
                </div>

                <div class="form-group">
                    <label for="password">Password <span class="required">*</span></label>
                    <input type="password" id="password" name="password" required placeholder="Enter your password">
                </div>

                <button type="submit" id="loginBtn" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
            </form>

            <!-- Account Activation Area -->
            <div id="activationArea" style="display: none;">
                <div class="form-group">
                    <label for="emailDisplayActivation">Email Address</label>
                    <div class="email-display" id="emailDisplayActivation"></div>
                    <button type="button" class="btn-link" id="changeEmailBtnActivation">
                        <i class="fas fa-edit"></i> Change Email
                    </button>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    Your account is not activated yet. Please choose one of the following actions:
                </div>

                <div class="activation-buttons">
                    <button type="button" class="btn btn-primary" id="goToActivationBtn">
                        <i class="fas fa-key"></i> Enter Verification Code
                    </button>
                    <button type="button" class="btn btn-secondary" id="resendActivationBtn">
                        <i class="fas fa-envelope"></i> Resend Verification Code
                    </button>
                </div>
            </div>

            <!-- Registration Prompt Area -->
            <div id="registerArea" style="display: none;">
                <div class="form-group">
                    <label for="emailDisplayRegister">Email Address</label>
                    <div class="email-display" id="emailDisplayRegister"></div>
                    <button type="button" class="btn-link" id="changeEmailBtnRegister">
                        <i class="fas fa-edit"></i> Change Email
                    </button>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    This email is not registered yet. Please register an account first.
                </div>

                <div class="register-buttons">
                    <button type="button" class="btn btn-primary" id="goToRegisterBtn">
                        <i class="fas fa-user-plus"></i> Register Now
                    </button>
                </div>
            </div>

            <div class="forgot-password">
                <a href="/password-reset">Forgot Password?</a>
            </div>

            <div class="register-link">
                <p>Don't have an account? <a href="/register">Register Now</a></p>
            </div>
        </div>
    </div>

    <script>
        // Pass API configuration from Django to frontend
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };

        // Two-step login logic
        let currentEmail = '';

        // Step 1: Email verification
        document.getElementById('emailForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const email = document.getElementById('email').value;
            const messageDiv = document.getElementById('loginMessage');
            const emailBtn = document.getElementById('emailBtn');

            if (!email) {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Please enter your email address
                    </div>
                `;
                return;
            }

            emailBtn.disabled = true;
            emailBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            messageDiv.innerHTML = '';

            try {
                const response = await fetch(`{{ API_BASE_URL }}/api/check-email/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: email })
                });

                const result = await response.json();

                if (result.success) {
                    currentEmail = email;

                    if (result.action === 'login') {
                        // User is activated, show password input
                        showPasswordForm();
                    } else if (result.action === 'activate') {
                        // User is not activated, show activation options
                        showActivationArea();
                    } else if (result.action === 'register') {
                        // User is not registered, show registration options
                        showRegisterArea();
                    }
                } else {
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Network error, please try again later
                    </div>
                `;
            } finally {
                emailBtn.disabled = false;
                emailBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Next';
            }
        });

        // Show password input form
        function showPasswordForm() {
            document.getElementById('emailForm').style.display = 'none';
            document.getElementById('passwordForm').style.display = 'block';
            document.getElementById('activationArea').style.display = 'none';
            document.getElementById('registerArea').style.display = 'none';
            document.getElementById('emailDisplay').textContent = currentEmail;
            document.getElementById('password').focus();
        }

        // Show activation area
        function showActivationArea() {
            document.getElementById('emailForm').style.display = 'none';
            document.getElementById('passwordForm').style.display = 'none';
            document.getElementById('activationArea').style.display = 'block';
            document.getElementById('registerArea').style.display = 'none';
            document.getElementById('emailDisplayActivation').textContent = currentEmail;
        }

        // Show registration area
        function showRegisterArea() {
            document.getElementById('emailForm').style.display = 'none';
            document.getElementById('passwordForm').style.display = 'none';
            document.getElementById('activationArea').style.display = 'none';
            document.getElementById('registerArea').style.display = 'block';
            document.getElementById('emailDisplayRegister').textContent = currentEmail;
        }

        // Return to email input
        function backToEmailForm() {
            document.getElementById('emailForm').style.display = 'block';
            document.getElementById('passwordForm').style.display = 'none';
            document.getElementById('activationArea').style.display = 'none';
            document.getElementById('registerArea').style.display = 'none';
            document.getElementById('loginMessage').innerHTML = '';
            currentEmail = '';
        }

        // Change email button events
        document.getElementById('changeEmailBtn').addEventListener('click', backToEmailForm);
        document.getElementById('changeEmailBtnActivation').addEventListener('click', backToEmailForm);
        document.getElementById('changeEmailBtnRegister').addEventListener('click', backToEmailForm);

        // Activation related button events
        document.getElementById('goToActivationBtn').addEventListener('click', () => {
            localStorage.setItem('pendingActivationEmail', currentEmail);
            window.location.href = `/verify-code?email=${encodeURIComponent(currentEmail)}`;
        });

        document.getElementById('resendActivationBtn').addEventListener('click', async () => {
            const btn = document.getElementById('resendActivationBtn');
            const messageDiv = document.getElementById('loginMessage');

            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';

            try {
                const response = await authAPI.resendVerification(currentEmail);

                if (response.success) {
                    messageDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            Verification code has been resent to your email
                        </div>
                    `;
                } else {
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            ${response.message}
                        </div>
                    `;
                }
            } catch (error) {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Failed to send, please try again later
                    </div>
                `;
            } finally {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-envelope"></i> Resend Verification Code';
            }
        });

        // Step 2: Password login
        document.getElementById('passwordForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const password = document.getElementById('password').value;
            const messageDiv = document.getElementById('loginMessage');
            const loginBtn = document.getElementById('loginBtn');

            if (!password) {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Please enter your password
                    </div>
                `;
                return;
            }

            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Signing In...';
            messageDiv.innerHTML = '';

            try {
                const response = await authAPI.login(currentEmail, password);

                if (response.success) {
                    messageDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            Login successful! Redirecting...
                        </div>
                    `;

                    // Save user information and tokens
                    localStorage.setItem('access_token', response.data.tokens.access);
                    localStorage.setItem('refresh_token', response.data.tokens.refresh);
                    localStorage.setItem('user_info', JSON.stringify(response.data.user));

                    setTimeout(() => {
                        console.log('🔐 Login successful, handling redirect...');

                        // Use the new auth navigation system
                        if (typeof authNav !== 'undefined') {
                            authNav.handlePostLogin();
                        } else {
                            // Fallback to home
                            window.location.replace('/');
                        }
                    }, 1500);
                } else {
                    messageDiv.innerHTML = `
                        <div class="alert alert-error">
                            <i class="fas fa-exclamation-circle"></i>
                            ${response.message}
                        </div>
                    `;
                }
            } catch (error) {
                messageDiv.innerHTML = `
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i>
                        Network error, please try again later
                    </div>
                `;
            } finally {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
            }
        });

        // Register button event
        document.getElementById('goToRegisterBtn').addEventListener('click', () => {
            window.location.href = `/register?email=${encodeURIComponent(currentEmail)}`;
        });

        // Handle URL parameters
        console.log('🔐 Login page initializing...');

        const urlParams = new URLSearchParams(window.location.search);
        const emailParam = urlParams.get('email');
        if (emailParam) {
            document.getElementById('email').value = emailParam;
        }

        // Handle next parameter for redirect after login
        const nextParam = urlParams.get('next');
        if (nextParam && typeof authNav !== 'undefined') {
            const decodedUrl = decodeURIComponent(nextParam);
            console.log('🔐 Saving redirect URL from next parameter:', decodedUrl);
            authNav.saveReturnUrl(decodedUrl);
        }

        const message = urlParams.get('message');
        if (message) {
            document.getElementById('loginMessage').innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    ${decodeURIComponent(message)}
                </div>
            `;
        }



    </script>
    <script src="{% static 'js/auth_api.js' %}?v=2.0"></script>
</body>
</html>
